
import { useState, useEffect, useCallback } from 'react';
import { LocationState, LocationData, Coordinates, IPLocationResponse } from '@/types/location';
import { locationStorage } from '@/utils/locationStorage';
import { geocoding } from '@/utils/geocoding';
import { toast } from '@/hooks/use-toast';

export function useEnhancedLocation() {
  const [state, setState] = useState<LocationState>({
    data: null,
    isLoading: true,
    error: null,
    hasPermission: null,
    isRefreshing: false
  });

  const [shouldPromptPermission, setShouldPromptPermission] = useState(false);

  // Check permission status with enhanced detection
  const checkPermissionStatus = useCallback(async () => {
    if (!navigator.permissions) {
      console.log('Navigator.permissions not available, defaulting to prompt');
      return 'prompt';
    }

    try {
      const result = await navigator.permissions.query({ name: 'geolocation' });
      console.log('Permission query result:', result.state);
      return result.state;
    } catch (error) {
      console.warn('Permission query failed:', error);
      return 'prompt';
    }
  }, []);

  // Get location via IP geolocation with enhanced error handling
  const getIPLocation = useCallback(async (): Promise<LocationData | null> => {
    try {
      console.log('Attempting IP-based location detection...');
      const response = await fetch('https://ipapi.co/json/');
      if (!response.ok) throw new Error(`IP location API failed: ${response.status}`);

      const ipData: IPLocationResponse = await response.json();
      console.log('IP location data received:', ipData);

      const coordinates: Coordinates = {
        latitude: ipData.latitude,
        longitude: ipData.longitude,
        accuracy: 10000 // IP location is less accurate
      };

      console.log('Reverse geocoding IP coordinates...');
      const address = await geocoding.reverseGeocode(coordinates);

      const locationData = {
        coordinates,
        address,
        source: 'ip' as const,
        timestamp: Date.now(),
        accuracy: 10000
      };

      console.log('IP location data created successfully:', locationData);
      return locationData;
    } catch (error) {
      console.error('IP location failed:', error);
      return null;
    }
  }, []);

  // Get high-accuracy GPS location with detailed error handling
  const getGPSLocation = useCallback(async (): Promise<LocationData | null> => {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        console.warn('Geolocation not supported by browser');
        resolve(null);
        return;
      }

      console.log('Attempting GPS location detection...');

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            console.log('GPS position obtained:', position.coords);

            const coordinates: Coordinates = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy
            };

            console.log('Reverse geocoding GPS coordinates...');
            const address = await geocoding.reverseGeocode(coordinates);

            const locationData: LocationData = {
              coordinates,
              address,
              source: 'gps',
              timestamp: Date.now(),
              accuracy: position.coords.accuracy
            };

            console.log('GPS location data created successfully:', locationData);
            resolve(locationData);
          } catch (error) {
            console.error('GPS location processing failed:', error);
            resolve(null);
          }
        },
        (error) => {
          console.error('GPS location failed with error:', error.code, error.message);
          // Log specific error types for debugging
          switch(error.code) {
            case error.PERMISSION_DENIED:
              console.error('GPS: User denied the request for Geolocation');
              break;
            case error.POSITION_UNAVAILABLE:
              console.error('GPS: Location information is unavailable');
              break;
            case error.TIMEOUT:
              console.error('GPS: The request to get user location timed out');
              break;
            default:
              console.error('GPS: An unknown error occurred');
              break;
          }
          resolve(null);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000, // Increased timeout to 15 seconds
          maximumAge: 300000 // 5 minutes cache
        }
      );
    });
  }, []);

  // Enhanced location detection with intelligent permission handling
  const detectLocation = useCallback(async (force = false, silent = false) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Try cached location first (if not forcing refresh)
      if (!force) {
        const cached = locationStorage.load();
        if (cached) {
          setState(prev => ({
            ...prev,
            data: cached,
            isLoading: false,
            hasPermission: true
          }));
          setShouldPromptPermission(false);
          return cached;
        }
      }

      // Check stored permission preference
      const storedPermission = locationStorage.loadPermission();
      const permissionStatus = await checkPermissionStatus();

      console.log('Permission status check:', { storedPermission, permissionStatus });
      setState(prev => ({ ...prev, hasPermission: permissionStatus === 'granted' }));

      let locationData: LocationData | null = null;

      // If user previously denied permission, don't try GPS again
      if (storedPermission?.status === 'denied' && storedPermission.userChoice) {
        console.log('User previously denied permission, using IP location');
        locationData = await getIPLocation();

        if (locationData && !silent) {
          toast({
            title: "Location detected",
            description: `Using approximate location: ${locationData.address.city || 'Current area'}`,
          });
        }
      } else {
        // Try GPS first if permission is granted or not yet asked
        if (permissionStatus === 'granted') {
          console.log('🔍 Permission granted, attempting GPS location...');
          locationData = await getGPSLocation();

          if (locationData) {
            // Save successful permission
            locationStorage.savePermission('granted', true);
            console.log('✅ GPS location successful, accuracy:', locationData.accuracy);
            if (!silent) {
              toast({
                title: "Location detected",
                description: `Using precise GPS location: ${locationData.address.area || locationData.address.city || 'Current location'}`,
              });
            }
          } else {
            // GPS failed even with permission - fallback to IP
            console.warn('⚠️ GPS failed despite granted permission, falling back to IP location');
            locationData = await getIPLocation();

            if (locationData && !silent) {
              toast({
                title: "Location detected",
                description: `GPS unavailable, using approximate location: ${locationData.address.city || 'Current area'}`,
                variant: "destructive"
              });
            }
          }
        } else if (permissionStatus === 'prompt') {
          // Check if user has made a choice before
          if (!locationStorage.hasUserMadeChoice()) {
            console.log('🔍 Permission prompt needed, showing permission request');
            setShouldPromptPermission(true);
          } else {
            console.log('🔍 User previously made choice, using IP location');
          }

          // Use IP location as fallback while waiting for user decision or if user previously chose
          locationData = await getIPLocation();

          if (locationData && !silent) {
            const message = !locationStorage.hasUserMadeChoice()
              ? `Using approximate location: ${locationData.address.city || 'Current area'}. Grant location access for better accuracy.`
              : `Using approximate location: ${locationData.address.city || 'Current area'}`;

            toast({
              title: "Location detected",
              description: message,
            });
          }
        } else {
          // Permission denied or other status - use IP location
          console.log('🔍 Permission not available, using IP location');
          locationData = await getIPLocation();

          if (locationData && !silent) {
            toast({
              title: "Location detected",
              description: `Using approximate location: ${locationData.address.city || 'Current area'}`,
            });
          }
        }
      }

      if (locationData) {
        locationStorage.save(locationData);
        setState(prev => ({
          ...prev,
          data: locationData,
          isLoading: false,
          error: null
        }));
        return locationData;
      } else {
        throw new Error('Unable to detect location');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Location detection failed';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }));

      if (!silent) {
        toast({
          title: "Location detection failed",
          description: "Please enable location access or enter your location manually",
          variant: "destructive"
        });
      }

      return null;
    }
  }, [checkPermissionStatus, getGPSLocation, getIPLocation]);

  // Request permission explicitly (called when user clicks "Allow Location")
  const requestLocationPermission = useCallback(async () => {
    console.log('🔍 Requesting location permission explicitly...');
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // First, try to trigger the browser's permission prompt by calling getCurrentPosition
      console.log('🔍 Triggering browser permission prompt...');

      const permissionResult = await new Promise<GeolocationPosition | null>((resolve) => {
        if (!navigator.geolocation) {
          console.error('❌ Geolocation not supported');
          resolve(null);
          return;
        }

        // This call should trigger the browser's permission prompt
        navigator.geolocation.getCurrentPosition(
          (position) => {
            console.log('✅ Permission granted by user, GPS position obtained');
            resolve(position);
          },
          (error) => {
            console.error('❌ Permission denied or GPS failed:', error.code, error.message);
            resolve(null);
          },
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0 // Force fresh location request
          }
        );
      });

      if (permissionResult) {
        // Permission granted and GPS worked
        console.log('✅ Processing GPS location data...');

        const coordinates: Coordinates = {
          latitude: permissionResult.coords.latitude,
          longitude: permissionResult.coords.longitude,
          accuracy: permissionResult.coords.accuracy
        };

        const address = await geocoding.reverseGeocode(coordinates);

        const locationData: LocationData = {
          coordinates,
          address,
          source: 'gps',
          timestamp: Date.now(),
          accuracy: permissionResult.coords.accuracy
        };

        console.log('✅ GPS location data created successfully:', locationData);

        locationStorage.savePermission('granted', true);
        locationStorage.save(locationData);

        setState(prev => ({
          ...prev,
          data: locationData,
          isLoading: false,
          hasPermission: true,
          error: null
        }));

        setShouldPromptPermission(false);

        toast({
          title: "Location access granted",
          description: `Using precise GPS location: ${locationData.address.area || locationData.address.city || 'Current location'}`,
        });

        return locationData;
      } else {
        // Permission denied or GPS failed - fallback to IP
        console.warn('⚠️ GPS failed, falling back to IP location');

        const ipLocation = await getIPLocation();
        if (ipLocation) {
          locationStorage.save(ipLocation);
          setState(prev => ({
            ...prev,
            data: ipLocation,
            isLoading: false,
            hasPermission: false,
            error: null
          }));

          setShouldPromptPermission(false);

          toast({
            title: "Using approximate location",
            description: `GPS unavailable, using approximate location: ${ipLocation.address.city || 'Current area'}`,
          });

          return ipLocation;
        } else {
          throw new Error('Both GPS and IP location failed');
        }
      }
    } catch (error) {
      console.error('❌ Location permission request failed:', error);

      // Save that permission was denied
      locationStorage.savePermission('denied', true);

      setState(prev => ({
        ...prev,
        isLoading: false,
        hasPermission: false,
        error: 'Location access denied'
      }));

      setShouldPromptPermission(false);

      toast({
        title: "Location access denied",
        description: "You can enable location access later in your browser settings.",
        variant: "destructive",
      });

      return null;
    }
  }, [getIPLocation]);

  // Deny permission explicitly (called when user clicks "Skip" or "Deny")
  const denyLocationPermission = useCallback(async () => {
    locationStorage.savePermission('denied', true);
    setShouldPromptPermission(false);

    setState(prev => ({ ...prev, hasPermission: false }));

    // Use IP location as fallback
    const ipLocation = await getIPLocation();
    if (ipLocation) {
      locationStorage.save(ipLocation);
      setState(prev => ({
        ...prev,
        data: ipLocation,
        isLoading: false,
        error: null
      }));

      toast({
        title: "Using approximate location",
        description: `Location set to: ${ipLocation.address.city || 'Current area'}`,
      });

      return ipLocation;
    }

    setState(prev => ({ ...prev, isLoading: false }));
    return null;
  }, [getIPLocation]);

  // Refresh location
  const refreshLocation = useCallback(async () => {
    setState(prev => ({ ...prev, isRefreshing: true }));
    await detectLocation(true);
    setState(prev => ({ ...prev, isRefreshing: false }));
  }, [detectLocation]);

  // Set manual location
  const setManualLocation = useCallback(async (address: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const coordinates = await geocoding.forwardGeocode(address);
      
      if (!coordinates) {
        throw new Error('Address not found');
      }

      const resolvedAddress = await geocoding.reverseGeocode(coordinates);
      
      const locationData: LocationData = {
        coordinates,
        address: resolvedAddress,
        source: 'manual',
        timestamp: Date.now()
      };

      locationStorage.save(locationData);
      setState(prev => ({
        ...prev,
        data: locationData,
        isLoading: false,
        error: null
      }));

      toast({
        title: "Location set",
        description: `Location set to: ${resolvedAddress.area || resolvedAddress.city || address}`,
      });

      return locationData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to set location';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }));
      
      toast({
        title: "Failed to set location",
        description: errorMessage,
        variant: "destructive"
      });
      
      return null;
    }
  }, []);

  // Clear location
  const clearLocation = useCallback(() => {
    locationStorage.clear();
    setState({
      data: null,
      isLoading: false,
      error: null,
      hasPermission: null,
      isRefreshing: false
    });
  }, []);

  // Auto-detect on mount (silently)
  useEffect(() => {
    detectLocation(false, true); // Silent detection on mount
  }, []);

  // Test GPS location directly (for debugging)
  const testGPSLocation = useCallback(async () => {
    console.log('🧪 Testing GPS location directly...');

    // Check browser support
    if (!navigator.geolocation) {
      console.error('❌ Geolocation not supported by this browser');
      return null;
    }

    // Check permission status first
    try {
      const permissionStatus = await checkPermissionStatus();
      console.log('🔍 Current permission status:', permissionStatus);
    } catch (error) {
      console.warn('⚠️ Could not check permission status:', error);
    }

    // Test GPS location
    const result = await getGPSLocation();
    console.log('🧪 GPS test result:', result);

    if (!result) {
      console.log('🔍 GPS failed, testing if browser blocks location requests...');

      // Try a simple position request to see what happens
      return new Promise<LocationData | null>((resolve) => {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            console.log('✅ Simple GPS test succeeded:', position.coords);
            resolve(null); // We don't process it, just test if it works
          },
          (error) => {
            console.error('❌ Simple GPS test failed:', error.code, error.message);
            console.log('🔍 Error codes: 1=PERMISSION_DENIED, 2=POSITION_UNAVAILABLE, 3=TIMEOUT');
            resolve(null);
          },
          { enableHighAccuracy: false, timeout: 5000, maximumAge: 0 }
        );
      });
    }

    return result;
  }, [getGPSLocation, checkPermissionStatus]);

  // Check browser security context for location access
  const checkBrowserSecurity = useCallback(() => {
    const isSecureContext = window.isSecureContext;
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;

    console.log('🔒 Browser Security Context Check:');
    console.log('  - Secure Context (HTTPS/localhost):', isSecureContext);
    console.log('  - Protocol:', protocol);
    console.log('  - Hostname:', hostname);
    console.log('  - Geolocation Available:', !!navigator.geolocation);

    if (!isSecureContext && hostname !== 'localhost') {
      console.warn('⚠️ Location API requires HTTPS or localhost!');
      return false;
    }

    return true;
  }, []);

  return {
    ...state,
    shouldPromptPermission,
    detectLocation,
    refreshLocation,
    setManualLocation,
    clearLocation,
    requestLocationPermission,
    denyLocationPermission,
    testGPSLocation, // For debugging purposes
    checkBrowserSecurity // For debugging security issues
  };
}

// Export distance calculation function for compatibility
export function calculateDistance(
  lat1?: number | null,
  lon1?: number | null,
  lat2?: number | null,
  lon2?: number | null
): number | null {
  if (!lat1 || !lon1 || !lat2 || !lon2) return null;
  
  const R = 6371; // Radius of the Earth in km
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2); 
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a)); 
  const distance = R * c; // Distance in km
  
  return distance;
}
