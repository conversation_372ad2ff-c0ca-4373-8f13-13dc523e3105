import React, { useState } from 'react';
import { MapPin, Navigation, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export interface LocationPermissionRequestProps {
  onPermissionGranted: () => void;
  onPermissionDenied?: () => void;
  isRequesting?: boolean;
}

export const LocationPermissionRequest: React.FC<LocationPermissionRequestProps> = ({
  onPermissionGranted,
  onPermissionDenied,
  isRequesting = false
}) => {
  const handleRequestPermission = () => {
    if (onPermissionGranted) {
      onPermissionGranted();
    }
  };

  const handleSkip = () => {
    if (onPermissionDenied) {
      onPermissionDenied();
    }
  };

  return (
    <Card className="border border-emerald-500/20 bg-gradient-to-r from-emerald-900/20 to-green-900/20 backdrop-blur-sm animate-fade-in shadow-lg">
      <CardContent className="p-6">
        <div className="flex flex-col items-center text-center space-y-4">
          {/* Icon with animation */}
          <div className="relative">
            <div className="absolute inset-0 bg-emerald-500/20 rounded-full animate-pulse"></div>
            <div className="relative bg-emerald-900/80 p-4 rounded-full">
              <Target className="h-8 w-8 text-emerald-400" />
            </div>
          </div>

          {/* Content */}
          <div className="space-y-2">
            <h3 className="text-xl font-bold text-white">Find venues near you</h3>
            <p className="text-sm text-gray-300 max-w-md">
              Get personalized venue recommendations and accurate distances by allowing location access
            </p>
          </div>

          {/* Benefits */}
          <div className="flex flex-wrap justify-center gap-4 text-xs text-emerald-300">
            <div className="flex items-center gap-1">
              <Navigation className="h-3 w-3" />
              <span>Accurate distances</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              <span>Nearby venues</span>
            </div>
            <div className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              <span>Better recommendations</span>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={handleSkip}
              className="px-6 border-emerald-500/40 text-emerald-300 hover:bg-emerald-900/20 hover:text-white transition-all duration-200"
            >
              Maybe later
            </Button>

            <Button
              onClick={handleRequestPermission}
              disabled={isRequesting}
              className="px-6 bg-emerald-600 hover:bg-emerald-500 text-white transition-all duration-200 shadow-lg hover:shadow-emerald-500/25"
            >
              {isRequesting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>Getting location...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span>Allow Location</span>
                </div>
              )}
            </Button>
          </div>

          {/* Privacy note */}
          <p className="text-xs text-gray-400 max-w-sm">
            Your location is only used to show nearby venues and is stored securely on your device
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
