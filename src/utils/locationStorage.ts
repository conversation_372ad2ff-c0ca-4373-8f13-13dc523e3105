
import { LocationData } from '@/types/location';

const LOCATION_STORAGE_KEY = 'grid2play_location';
const PERMISSION_STORAGE_KEY = 'grid2play_location_permission';
const LOCATION_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours (like Swiggy/Zomato)
const PERMISSION_CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days

interface LocationPermissionData {
  status: 'granted' | 'denied' | 'prompt';
  timestamp: number;
  userChoice: boolean; // true if user explicitly made a choice
}

export const locationStorage = {
  save: (location: LocationData): void => {
    try {
      localStorage.setItem(LOCATION_STORAGE_KEY, JSON.stringify(location));
    } catch (error) {
      console.warn('Failed to save location to localStorage:', error);
    }
  },

  load: (): LocationData | null => {
    try {
      const stored = localStorage.getItem(LOCATION_STORAGE_KEY);
      if (!stored) return null;

      const location: LocationData = JSON.parse(stored);
      
      // Check if cache is expired
      if (Date.now() - location.timestamp > LOCATION_CACHE_DURATION) {
        localStorage.removeItem(LOCATION_STORAGE_KEY);
        return null;
      }

      return location;
    } catch (error) {
      console.warn('Failed to load location from localStorage:', error);
      localStorage.removeItem(LOCATION_STORAGE_KEY);
      return null;
    }
  },

  clear: (): void => {
    try {
      localStorage.removeItem(LOCATION_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear location from localStorage:', error);
    }
  },

  isExpired: (location: LocationData): boolean => {
    return Date.now() - location.timestamp > LOCATION_CACHE_DURATION;
  },

  // Permission management methods
  savePermission: (status: 'granted' | 'denied' | 'prompt', userChoice: boolean = false): void => {
    try {
      const permissionData: LocationPermissionData = {
        status,
        timestamp: Date.now(),
        userChoice
      };
      localStorage.setItem(PERMISSION_STORAGE_KEY, JSON.stringify(permissionData));
    } catch (error) {
      console.warn('Failed to save permission to localStorage:', error);
    }
  },

  loadPermission: (): LocationPermissionData | null => {
    try {
      const stored = localStorage.getItem(PERMISSION_STORAGE_KEY);
      if (!stored) return null;

      const permission: LocationPermissionData = JSON.parse(stored);

      // Check if permission cache is expired
      if (Date.now() - permission.timestamp > PERMISSION_CACHE_DURATION) {
        localStorage.removeItem(PERMISSION_STORAGE_KEY);
        return null;
      }

      return permission;
    } catch (error) {
      console.warn('Failed to load permission from localStorage:', error);
      localStorage.removeItem(PERMISSION_STORAGE_KEY);
      return null;
    }
  },

  clearPermission: (): void => {
    try {
      localStorage.removeItem(PERMISSION_STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear permission from localStorage:', error);
    }
  },

  // Check if user has made an explicit choice about location
  hasUserMadeChoice: (): boolean => {
    const permission = locationStorage.loadPermission();
    return permission?.userChoice === true;
  },

  // Get the last known permission status
  getLastPermissionStatus: (): 'granted' | 'denied' | 'prompt' | null => {
    const permission = locationStorage.loadPermission();
    return permission?.status || null;
  }
};
