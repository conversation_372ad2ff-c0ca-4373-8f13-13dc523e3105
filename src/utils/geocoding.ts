
import { Address, Coordinates, GeocodingResponse } from '@/types/location';

// Multiple geocoding services for fallback
const GEOCODING_SERVICES = {
  // Primary: Free geocoding service with CORS support
  GEOCODE_MAPS: 'https://geocode.maps.co',
  // Fallback: OpenStreetMap Nominatim (may have CORS issues)
  NOMINATIM: 'https://nominatim.openstreetmap.org',
  // Alternative: Another free service
  PHOTON: 'https://photon.komoot.io'
};

const REQUEST_DELAY = 1000; // 1 second delay between requests for rate limiting
let lastRequestTime = 0;

const delayRequest = async (): Promise<void> => {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < REQUEST_DELAY) {
    await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY - timeSinceLastRequest));
  }

  lastRequestTime = Date.now();
};

// Create fallback address when geocoding fails
const createFallbackAddress = (coordinates: Coordinates): Address => {
  console.log('🗺️ Creating fallback address for coordinates:', coordinates);

  // Determine approximate location based on coordinates (India-specific)
  let city = 'Unknown City';
  let state = 'Unknown State';
  let country = 'India';

  // Basic India region detection based on coordinates
  if (coordinates.latitude >= 28.4 && coordinates.latitude <= 28.9 &&
      coordinates.longitude >= 76.8 && coordinates.longitude <= 77.3) {
    city = 'New Delhi';
    state = 'Delhi';
  } else if (coordinates.latitude >= 19.0 && coordinates.latitude <= 19.3 &&
             coordinates.longitude >= 72.7 && coordinates.longitude <= 73.0) {
    city = 'Mumbai';
    state = 'Maharashtra';
  } else if (coordinates.latitude >= 12.8 && coordinates.latitude <= 13.1 &&
             coordinates.longitude >= 77.4 && coordinates.longitude <= 77.8) {
    city = 'Bangalore';
    state = 'Karnataka';
  }

  return {
    area: 'Current Area',
    city,
    state,
    country,
    display_name: `${city}, ${state}, ${country}`,
    street: undefined,
    postal_code: undefined
  };
};

// Try multiple geocoding services with fallback
const tryReverseGeocode = async (coordinates: Coordinates, serviceUrl: string, serviceName: string): Promise<Address | null> => {
  try {
    console.log(`🗺️ Trying ${serviceName} for reverse geocoding...`);

    let url: string;
    let headers: Record<string, string> = {};

    if (serviceUrl === GEOCODING_SERVICES.GEOCODE_MAPS) {
      // geocode.maps.co API
      url = `${serviceUrl}/reverse?lat=${coordinates.latitude}&lon=${coordinates.longitude}&format=json`;
    } else if (serviceUrl === GEOCODING_SERVICES.PHOTON) {
      // Photon API
      url = `${serviceUrl}/reverse?lat=${coordinates.latitude}&lon=${coordinates.longitude}`;
    } else {
      // Nominatim API
      url = `${serviceUrl}/reverse?format=json&lat=${coordinates.latitude}&lon=${coordinates.longitude}&zoom=18&addressdetails=1`;
      headers['User-Agent'] = 'Grid2Play Sports Venue App';
    }

    const response = await fetch(url, {
      headers,
      mode: 'cors' // Explicitly request CORS
    });

    if (!response.ok) {
      throw new Error(`${serviceName} returned ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`✅ ${serviceName} response:`, data);

    // Parse response based on service
    if (serviceUrl === GEOCODING_SERVICES.GEOCODE_MAPS) {
      return {
        street: data.address?.road || data.address?.house_number,
        area: data.address?.suburb || data.address?.neighbourhood,
        city: data.address?.city || data.address?.town || data.address?.village,
        state: data.address?.state,
        country: data.address?.country,
        postal_code: data.address?.postcode,
        display_name: data.display_name || `${data.address?.city || 'Unknown'}, ${data.address?.state || 'Unknown'}`
      };
    } else if (serviceUrl === GEOCODING_SERVICES.PHOTON) {
      const props = data.features?.[0]?.properties;
      if (!props) throw new Error('No properties in Photon response');

      return {
        street: props.street,
        area: props.district,
        city: props.city,
        state: props.state,
        country: props.country,
        postal_code: props.postcode,
        display_name: props.name || `${props.city || 'Unknown'}, ${props.state || 'Unknown'}`
      };
    } else {
      // Nominatim format
      return {
        street: data.address?.road,
        area: data.address?.suburb,
        city: data.address?.city,
        state: data.address?.state,
        country: data.address?.country,
        postal_code: data.address?.postcode,
        display_name: data.display_name
      };
    }
  } catch (error) {
    console.error(`❌ ${serviceName} failed:`, error);
    return null;
  }
};

export const geocoding = {
  // Convert coordinates to address with multiple service fallback
  reverseGeocode: async (coordinates: Coordinates): Promise<Address> => {
    console.log('🗺️ Starting reverse geocoding for:', coordinates);
    await delayRequest();

    // Try multiple services in order of preference
    const services = [
      { url: GEOCODING_SERVICES.GEOCODE_MAPS, name: 'Geocode.maps.co' },
      { url: GEOCODING_SERVICES.PHOTON, name: 'Photon' },
      { url: GEOCODING_SERVICES.NOMINATIM, name: 'Nominatim' }
    ];

    for (const service of services) {
      const result = await tryReverseGeocode(coordinates, service.url, service.name);
      if (result) {
        console.log(`✅ Reverse geocoding successful with ${service.name}:`, result);
        return result;
      }
    }

    // All services failed - return fallback address
    console.warn('⚠️ All geocoding services failed, using fallback address');
    return createFallbackAddress(coordinates);
  },

  // Convert address to coordinates with fallback
  forwardGeocode: async (address: string): Promise<Coordinates | null> => {
    console.log('🗺️ Forward geocoding for address:', address);
    await delayRequest();

    // Try geocode.maps.co first (better CORS support)
    try {
      const response = await fetch(
        `${GEOCODING_SERVICES.GEOCODE_MAPS}/search?q=${encodeURIComponent(address)}&format=json&limit=1`,
        { mode: 'cors' }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.length > 0) {
          console.log('✅ Forward geocoding successful with geocode.maps.co');
          return {
            latitude: parseFloat(data[0].lat),
            longitude: parseFloat(data[0].lon)
          };
        }
      }
    } catch (error) {
      console.warn('⚠️ geocode.maps.co forward geocoding failed:', error);
    }

    // Fallback to Nominatim
    try {
      const response = await fetch(
        `${GEOCODING_SERVICES.NOMINATIM}/search?format=json&q=${encodeURIComponent(address)}&limit=1`,
        {
          headers: { 'User-Agent': 'Grid2Play Sports Venue App' },
          mode: 'cors'
        }
      );

      if (response.ok) {
        const data: GeocodingResponse[] = await response.json();
        if (data.length > 0) {
          console.log('✅ Forward geocoding successful with Nominatim');
          return {
            latitude: parseFloat(data[0].lat),
            longitude: parseFloat(data[0].lon)
          };
        }
      }
    } catch (error) {
      console.error('❌ All forward geocoding services failed:', error);
    }

    return null;
  },

  // Search for address suggestions with fallback
  searchAddresses: async (query: string): Promise<Address[]> => {
    if (query.length < 3) return [];

    console.log('🗺️ Searching addresses for query:', query);
    await delayRequest();

    // Try geocode.maps.co first
    try {
      const response = await fetch(
        `${GEOCODING_SERVICES.GEOCODE_MAPS}/search?q=${encodeURIComponent(query)}&format=json&limit=5`,
        { mode: 'cors' }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.length > 0) {
          console.log('✅ Address search successful with geocode.maps.co');
          return data.map((item: any) => ({
            street: item.address?.road,
            area: item.address?.suburb || item.address?.neighbourhood,
            city: item.address?.city || item.address?.town,
            state: item.address?.state,
            country: item.address?.country,
            postal_code: item.address?.postcode,
            display_name: item.display_name || `${item.address?.city || 'Unknown'}, ${item.address?.state || 'Unknown'}`
          }));
        }
      }
    } catch (error) {
      console.warn('⚠️ geocode.maps.co address search failed:', error);
    }

    // Fallback to Nominatim
    try {
      const response = await fetch(
        `${GEOCODING_SERVICES.NOMINATIM}/search?format=json&q=${encodeURIComponent(query)}&limit=5&addressdetails=1`,
        {
          headers: { 'User-Agent': 'Grid2Play Sports Venue App' },
          mode: 'cors'
        }
      );

      if (response.ok) {
        const data: GeocodingResponse[] = await response.json();
        console.log('✅ Address search successful with Nominatim');
        return data.map(item => ({
          street: item.address?.road,
          area: item.address?.suburb,
          city: item.address?.city,
          state: item.address?.state,
          country: item.address?.country,
          postal_code: item.address?.postcode,
          display_name: item.display_name
        }));
      }
    } catch (error) {
      console.error('❌ All address search services failed:', error);
    }

    return [];
  }
}
};
