
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Eye, EyeOff, Lock, Phone } from 'lucide-react';
import Header from '../components/Header';
import ForgotPasswordModal from '../components/ForgotPasswordModal';
import { toast } from '@/components/ui/use-toast';

import { validatePhone, sanitizeInput } from '@/utils/security';
import { smsAuthService } from '@/services/smsAuthService';

const Login: React.FC = () => {
  // Phone login fields
  const [phone, setPhone] = useState('');
  const [countryCode, setCountryCode] = useState('+91');

  // Common fields
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Forgot password modal
  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);

  // Form validation and loading states
  const [phoneError, setPhoneError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Validate phone login form
  const validatePhoneForm = () => {
    let isValid = true;
    setPhoneError('');
    setPasswordError('');

    if (!phone || !validatePhone(countryCode + phone)) {
      setPhoneError('Please enter a valid phone number');
      isValid = false;
    }

    if (!password || password.length < 6) {
      setPasswordError('Password must be at least 6 characters long');
      isValid = false;
    }

    return isValid;
  };

  // Handle phone + password login
  const handlePhonePasswordLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePhoneForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const fullPhone = countryCode + sanitizeInput(phone);

      const result = await smsAuthService.signInWithPhone(fullPhone, password);

      if (result.success) {
        toast({
          title: "Login successful",
          description: "Welcome back to Grid2Play!",
        });
      } else {
        toast({
          title: "Login failed",
          description: result.error || "Please check your credentials and try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Login failed",
        description: "Please check your credentials and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };









  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-[#0F2419] to-[#1E3B2C] relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-[#2E7D32]/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-green-600/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <Header />

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4 pt-20">
        <div className="w-full max-w-md">
          {/* Main Card */}
          <div className="backdrop-blur-2xl bg-gradient-to-br from-black/60 via-[#0F2419]/80 to-black/60 border border-[#2E7D32]/30 rounded-3xl shadow-2xl overflow-hidden relative">
            {/* Glow Effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-[#2E7D32]/20 via-transparent to-emerald-500/20 opacity-50 blur-xl"></div>

            {/* Content */}
            <div className="relative z-10">
              {/* Header */}
              <div className="px-6 sm:px-8 py-8 sm:py-10 text-center">
                {/* Logo with Animation */}
                <div className="relative mb-6">
                  <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-[#2E7D32] via-emerald-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto shadow-2xl transform hover:scale-105 transition-all duration-300 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                    <span className="text-2xl sm:text-3xl font-bold text-white relative z-10">G2P</span>
                    {/* Shine Effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 -translate-x-full animate-shine"></div>
                  </div>
                  {/* Floating Particles */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                    <div className="w-2 h-2 bg-[#2E7D32] rounded-full animate-bounce delay-100"></div>
                  </div>
                  <div className="absolute top-4 right-1/4">
                    <div className="w-1 h-1 bg-emerald-400 rounded-full animate-ping delay-300"></div>
                  </div>
                  <div className="absolute top-4 left-1/4">
                    <div className="w-1 h-1 bg-green-500 rounded-full animate-ping delay-700"></div>
                  </div>
                </div>

                {/* Welcome Text */}
                <div className="space-y-2">
                  <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-white via-gray-100 to-emerald-100 bg-clip-text text-transparent">
                    Welcome Back
                  </h1>
                  <p className="text-gray-400 text-sm sm:text-base font-medium">
                    Sign in to your Grid2Play account
                  </p>
                  <div className="flex items-center justify-center space-x-2 mt-4">
                    <div className="w-8 h-0.5 bg-gradient-to-r from-transparent to-[#2E7D32]"></div>
                    <div className="w-2 h-2 bg-[#2E7D32] rounded-full animate-pulse"></div>
                    <div className="w-8 h-0.5 bg-gradient-to-l from-transparent to-[#2E7D32]"></div>
                  </div>
                </div>
              </div>

              {/* Phone-First Authentication Header */}
              <div className="mb-8 text-center">
                <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#2E7D32]/20 to-emerald-500/20 rounded-2xl border border-[#2E7D32]/40 backdrop-blur-sm">
                  <Phone className="h-5 w-5 text-emerald-400 mr-3" />
                  <span className="text-emerald-300 text-sm font-semibold">Phone + Password Login</span>
                </div>
                <p className="text-gray-400 text-sm mt-3 font-medium">Secure authentication with your verified phone number</p>
              </div>

              {/* Phone Login Form */}
              <div className="px-6 sm:px-8 pb-8">
                <form onSubmit={handlePhonePasswordLogin} className="space-y-6">
                  {/* Phone Number Field */}
                  <div className="space-y-2">
                    <label htmlFor="phone-login" className="block text-sm font-semibold text-emerald-300 mb-3">
                      Phone Number
                    </label>
                    <div className="flex gap-3">
                      <select
                        value={countryCode}
                        onChange={(e) => setCountryCode(e.target.value)}
                        className="w-24 p-4 border border-[#2E7D32]/40 bg-black/60 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-transparent transition-all text-sm backdrop-blur-sm hover:bg-black/80"
                      >
                        <option value="+91">🇮🇳 +91</option>
                        <option value="+1">🇺🇸 +1</option>
                        <option value="+44">🇬🇧 +44</option>
                        <option value="+971">🇦🇪 +971</option>
                        <option value="+65">🇸🇬 +65</option>
                      </select>
                      <div className="relative flex-1">
                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                          <Phone className="h-5 w-5 text-emerald-400" />
                        </div>
                        <input
                          id="phone-login"
                          type="tel"
                          value={phone}
                          onChange={(e) => {
                            setPhone(e.target.value);
                            setPhoneError('');
                          }}
                          className={`pl-12 w-full p-4 border ${phoneError ? 'border-red-500/60' : 'border-[#2E7D32]/40'} bg-black/60 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-transparent transition-all text-base backdrop-blur-sm hover:bg-black/80 placeholder-gray-500`}
                          placeholder="Enter your phone number"
                          required
                          maxLength={15}
                        />
                      </div>
                    </div>
                    {phoneError && <p className="text-red-400 text-sm mt-2 font-medium">{phoneError}</p>}
                  </div>

                  {/* Password Field */}
                  <div className="space-y-2">
                    <label htmlFor="phone-password" className="block text-sm font-semibold text-emerald-300 mb-3">
                      Password
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <Lock className="h-5 w-5 text-emerald-400" />
                      </div>
                      <input
                        id="phone-password"
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => {
                          setPassword(e.target.value);
                          setPasswordError('');
                        }}
                        className={`pl-12 pr-12 w-full p-4 border ${passwordError ? 'border-red-500/60' : 'border-[#2E7D32]/40'} bg-black/60 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:border-transparent transition-all text-base backdrop-blur-sm hover:bg-black/80 placeholder-gray-500`}
                        placeholder="Enter your password"
                        required
                        maxLength={128}
                      />
                      <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="text-emerald-400 hover:text-white focus:outline-none transition-colors"
                        >
                          {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                        </button>
                      </div>
                    </div>
                    {passwordError && <p className="text-red-400 text-sm mt-2 font-medium">{passwordError}</p>}
                  </div>

                  {/* Submit Button */}
                  <div className="pt-4">
                    <button
                      type="submit"
                      className="w-full py-4 px-6 bg-gradient-to-r from-[#2E7D32] via-emerald-500 to-green-600 hover:from-green-600 hover:via-emerald-600 hover:to-[#2E7D32] text-white rounded-xl font-bold shadow-2xl transition-all flex justify-center items-center transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#2E7D32] focus:ring-offset-2 focus:ring-offset-black/50 text-lg relative overflow-hidden group"
                      disabled={isLoading}
                    >
                      {/* Button Shine Effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                      {isLoading ? (
                        <span className="flex items-center relative z-10">
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Signing In...
                        </span>
                      ) : (
                        <span className="flex items-center gap-3 relative z-10">
                          <Lock className="h-5 w-5" />
                          Sign In with Password
                        </span>
                      )}
                    </button>
                  </div>

                  {/* Forgot Password Link */}
                  <div className="text-center pt-4">
                    <button
                      type="button"
                      onClick={() => setShowForgotPasswordModal(true)}
                      className="text-emerald-400 hover:text-emerald-300 text-sm font-medium transition-colors hover:underline"
                    >
                      Forgot Password?
                    </button>
                  </div>
                </form>

                {/* Sign Up Link */}
                <div className="mt-8 text-center border-t border-[#2E7D32]/20 pt-6">
                  <p className="text-gray-400 text-base mb-4">
                    Don't have an account?
                  </p>
                  <Link
                    to="/register"
                    className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-black/60 to-[#0F2419]/60 border border-[#2E7D32]/40 text-emerald-300 hover:text-white rounded-xl font-semibold transition-all transform hover:scale-[1.02] hover:border-[#2E7D32]/60 backdrop-blur-sm"
                  >
                    <span>Create Account</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Forgot Password Modal */}
      <ForgotPasswordModal
        isOpen={showForgotPasswordModal}
        onClose={() => setShowForgotPasswordModal(false)}
      />
    </div>
  );
};

export default Login;