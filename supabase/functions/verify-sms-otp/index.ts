import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Parse request body
    const { phone, otp } = await req.json()

    if (!phone || !otp) {
      return new Response(
        JSON.stringify({ error: 'Phone number and OTP are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Clean and format phone number
    const cleanPhone = phone.replace(/[\s()-]/g, '')
    const formattedPhone = cleanPhone.startsWith('+91') ? cleanPhone : 
                          cleanPhone.startsWith('91') ? '+' + cleanPhone : 
                          '+91' + cleanPhone

    // Validate OTP format
    if (!/^\d{6}$/.test(otp)) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Invalid OTP format. Must be 6 digits.' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Verifying SMS OTP for phone:', formattedPhone)

    // Validate OTP directly from pending_whatsapp_users table
    const { data: pendingUser, error: queryError } = await supabaseAdmin
      .from('pending_whatsapp_users')
      .select('*')
      .eq('phone', formattedPhone)
      .eq('purpose', 'registration')
      .single()

    if (queryError) {
      console.error('Error querying pending SMS OTP:', queryError)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'OTP validation failed'
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!pendingUser) {
      console.log('No pending SMS OTP found for phone:', formattedPhone)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'No OTP request found. Please request a new OTP.'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Check if OTP has expired
    const now = new Date()
    const expiresAt = new Date(pendingUser.otp_expires_at)

    if (now > expiresAt) {
      console.log('Expired OTP for phone:', formattedPhone)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'OTP has expired. Please request a new OTP.'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Check if OTP matches
    if (pendingUser.otp_code !== otp) {
      console.log('Invalid OTP for phone:', formattedPhone, 'Expected:', pendingUser.otp_code.substring(0, 2) + '****', 'Received:', otp.substring(0, 2) + '****')

      // Increment attempts
      const { error: updateError } = await supabaseAdmin
        .from('pending_whatsapp_users')
        .update({
          attempts: (pendingUser.attempts || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq('phone', formattedPhone)

      if (updateError) {
        console.error('Error updating OTP attempts:', updateError)
      }

      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid OTP. Please check and try again.'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Check if too many attempts
    if ((pendingUser.attempts || 0) >= 3) {
      console.log('Too many OTP attempts for phone:', formattedPhone)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Too many failed attempts. Please request a new OTP.'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // OTP is valid, create Supabase user
    const userData = {
      full_name: pendingUser.full_name,
      password_hash: pendingUser.password_hash
    }
    console.log('Creating Supabase user for verified phone:', formattedPhone)

    // Create user in Supabase Auth with temporary email (same as WhatsApp flow)
    const tempEmail = `${crypto.randomUUID()}@temp.grid2play.com`

    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: tempEmail,
      phone: formattedPhone,
      password: userData.password_hash,
      email_confirm: true, // Confirm temp email to avoid verification issues
      phone_confirm: true, // Mark phone as confirmed
      user_metadata: {
        full_name: userData.full_name,
        phone: formattedPhone,
        registration_method: 'sms', // Changed from 'whatsapp' to 'sms'
        temp_email: true
      }
    })

    if (authError) {
      console.error('Error creating Supabase user:', authError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Failed to create user account' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Supabase user created successfully:', authData.user.id)

    // Update user profile with phone verification
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .update({
        phone: formattedPhone,
        phone_verified: true,
        full_name: userData.full_name,
        email: tempEmail,
        updated_at: new Date().toISOString()
      })
      .eq('id', authData.user.id)

    if (profileError) {
      console.error('Error updating profile:', profileError)
      // Don't fail the request, user is created but profile update failed
    }

    // Clean up pending SMS OTP record
    const { error: cleanupError } = await supabaseAdmin
      .from('pending_whatsapp_users')
      .delete()
      .eq('phone', formattedPhone)
      .eq('purpose', 'registration')

    if (cleanupError) {
      console.error('Error cleaning up pending SMS OTP:', cleanupError)
      // Don't fail the request, cleanup is not critical
    }

    // Create session for the user
    const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
      type: 'magiclink',
      email: tempEmail,
      options: {
        redirectTo: `${Deno.env.get('SUPABASE_URL')}/auth/v1/callback`
      }
    })

    if (sessionError) {
      console.error('Error generating session:', sessionError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Failed to create user session' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'SMS OTP verified successfully',
        user: {
          id: authData.user.id,
          phone: formattedPhone,
          full_name: userData.full_name,
          email: tempEmail,
          phone_verified: true
        },
        sessionUrl: sessionData.properties?.action_link
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error in verify-sms-otp function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
