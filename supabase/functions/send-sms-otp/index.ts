import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface MSG91Response {
  type?: string
  message?: string
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get MSG91 OTP configuration from environment variables
    const authKey = Deno.env.get('MSG91_AUTH_KEY')
    const dltTemplateId = '1207175096915083168' // Working DLT template ID from successful request
    const senderId = 'GRDPLY' // Approved sender ID

    if (!authKey) {
      console.error('MSG91 OTP configuration missing - authKey not found')
      return new Response(
        JSON.stringify({ error: 'OTP service configuration not found' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse request body
    const { phone, fullName, password, purpose = 'registration', isLogin = false } = await req.json()

    if (!phone) {
      return new Response(
        JSON.stringify({ error: 'Phone number is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Clean phone number (remove spaces, dashes, parentheses)
    const cleanPhone = phone.replace(/[\s()-]/g, '')

    // Validate Indian phone number format
    if (!/^(\+91|91)?[6-9]\d{9}$/.test(cleanPhone)) {
      return new Response(
        JSON.stringify({ error: 'Invalid Indian phone number format' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Ensure phone starts with +91
    const formattedPhone = cleanPhone.startsWith('+91') ? cleanPhone : 
                          cleanPhone.startsWith('91') ? '+' + cleanPhone : 
                          '+91' + cleanPhone

    console.log('Processing SMS OTP request for phone:', formattedPhone, 'purpose:', purpose)

    // Check rate limiting using existing database function
    const { data: rateLimitResult, error: rateLimitError } = await supabaseAdmin
      .rpc('check_otp_rate_limit', { phone_number: formattedPhone })

    if (rateLimitError) {
      console.error('Rate limit check error:', rateLimitError)
      return new Response(
        JSON.stringify({ error: 'Rate limit check failed' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    if (!rateLimitResult) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Too many OTP requests. Please wait before requesting again.' 
        }),
        { 
          status: 429, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString()
    const otpExpiresAt = new Date(Date.now() + 5 * 60 * 1000) // 5 minutes (matching DLT template)

    // Hash password if provided (for registration)
    let passwordHash = null
    if (password && purpose === 'registration') {
      // Simple hash for compatibility with existing system
      const encoder = new TextEncoder()
      const data = encoder.encode(password)
      const hashBuffer = await crypto.subtle.digest('SHA-256', data)
      const hashArray = Array.from(new Uint8Array(hashBuffer))
      passwordHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
    }

    // Store OTP in pending_whatsapp_users table (reusing existing table structure)
    const { error: insertError } = await supabaseAdmin
      .from('pending_whatsapp_users')
      .upsert({
        phone: formattedPhone,
        full_name: fullName || '',
        password_hash: passwordHash || '',
        otp_code: otp,
        otp_expires_at: otpExpiresAt.toISOString(),
        attempts: 0,
        max_attempts: 3,
        last_otp_sent_at: new Date().toISOString(),
        purpose: purpose
      }, {
        onConflict: 'phone'
      })

    if (insertError) {
      console.error('Error storing SMS OTP:', insertError)
      return new Response(
        JSON.stringify({ error: 'Failed to store OTP' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Send SMS via MSG91 OTP API using the exact format from working request
    const mobileNumber = formattedPhone.replace('+91', '') // Remove +91 for MSG91 API

    // Create the message content exactly as in the working request
    const message = `Dear user, your Grid2Play verification code is ${otp}. It is valid for 5 minutes.\nDo not share this OTP with anyone.\n\n- Team Grid2Play, Drop Shot Sports Academy`

    // Create form data matching the working request format
    const formData = new URLSearchParams({
      'DLT_TE_ID': dltTemplateId,
      'campaign_name': 'OTP',
      'encrypt': '0',
      'message': message,
      'unicode': '1',
      'mobile': mobileNumber,
      'mobiles': mobileNumber,
      'otp': otp,
      'otp_expiry': '1440', // 24 hours as in working request
      'route': '106', // Route from working request
      'sender': senderId,
      'authkey': authKey
    })

    console.log('Sending SMS OTP via MSG91 OTP API (matching working format):', {
      phone: formattedPhone,
      sender: senderId,
      templateId: dltTemplateId,
      route: '106',
      purpose: purpose,
      otp: otp.substring(0, 2) + '****' // Log partial OTP for debugging
    })

    const response = await fetch('https://control.msg91.com/api/v5/otp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: formData.toString()
    })

    const responseText = await response.text()
    console.log('MSG91 OTP API response:', responseText)

    if (!response.ok) {
      console.error('MSG91 OTP API error:', responseText)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to send SMS OTP. Please try again.',
          details: responseText
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse response to check for success
    let responseData: MSG91Response
    try {
      responseData = JSON.parse(responseText)
    } catch (e) {
      // If response is not JSON, treat as success if status is ok
      responseData = { type: 'success', message: responseText }
    }

    // Check if MSG91 returned an error in the response body
    if (responseData.type === 'error' || responseData.message?.includes('error')) {
      console.error('MSG91 OTP API returned error:', responseData)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to send SMS OTP. Please check your phone number.',
          details: responseData.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'SMS OTP sent successfully',
        phone: formattedPhone,
        expires_in: 300 // 5 minutes in seconds
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error in send-sms-otp function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
