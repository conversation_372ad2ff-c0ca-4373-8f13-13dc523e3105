import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-requested-with',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Max-Age': '86400'
};
serve(async (req)=>{
  console.log('Phone login request received:', {
    method: req.method,
    origin: req.headers.get('origin'),
    userAgent: req.headers.get('user-agent'),
    timestamp: new Date().toISOString()
  });
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling CORS preflight request');
    return new Response('ok', {
      headers: corsHeaders,
      status: 200
    });
  }
  // Only allow POST requests
  if (req.method !== 'POST') {
    console.log('Method not allowed:', req.method);
    return new Response(JSON.stringify({
      success: false,
      error: 'Method not allowed'
    }), {
      status: 405,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
  try {
    const { phone, password } = await req.json();
    // Validate input
    if (!phone || !password) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Phone number and password are required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Format phone number to ensure it matches database format
    const formattedPhone = phone.startsWith('+') ? phone : `+${phone}`;
    // Initialize Supabase client with service role key
    const supabaseAdmin = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '');
    console.log('Simple phone login attempt for:', formattedPhone);
    // Find user profile with this phone number
    const { data: profile, error: profileError } = await supabaseAdmin.from('profiles').select('id, phone, phone_verified, full_name, email').eq('phone', formattedPhone).eq('phone_verified', true).single();
    if (profileError || !profile) {
      console.error('Profile lookup error:', profileError);
      return new Response(JSON.stringify({
        success: false,
        error: 'Phone number not found or not verified. Please register first.'
      }), {
        status: 404,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    console.log('Found verified profile:', profile.id);
    // Get the user from auth.users table
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.getUserById(profile.id);
    if (authError || !authUser.user) {
      console.error('Auth user lookup error:', authError);
      return new Response(JSON.stringify({
        success: false,
        error: 'User account not found'
      }), {
        status: 404,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Verify password by creating a temporary session
    // We'll use the user's email (temp or real) for authentication
    const userEmail = authUser.user.email;
    if (!userEmail) {
      return new Response(JSON.stringify({
        success: false,
        error: 'User authentication method not found'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    console.log('Attempting authentication for user:', profile.id);
    // Create a session using admin API (bypasses email/password validation)
    const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
      type: 'magiclink',
      email: userEmail,
      options: {
        redirectTo: `${req.headers.get('origin') || 'https://grid2play.com'}/dashboard`
      }
    });
    if (sessionError) {
      console.error('Session generation error:', sessionError);
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to create session'
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // For password verification, we need to validate the password
    // Let's try to sign in with email/password to verify the password is correct
    const supabase = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_ANON_KEY') ?? '');
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: userEmail,
      password: password
    });
    if (signInError) {
      console.error('Password verification failed:', signInError);
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid phone number or password'
      }), {
        status: 401,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    console.log('Phone login successful for user:', signInData.user?.id);
    // Return success with user data and session
    return new Response(JSON.stringify({
      success: true,
      user: {
        id: profile.id,
        phone: profile.phone,
        full_name: profile.full_name,
        email: profile.email?.includes('@temp.grid2play.com') ? null : profile.email,
        phone_verified: profile.phone_verified,
        email_verified: profile.email && !profile.email.includes('@temp.grid2play.com')
      },
      session: signInData.session
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Phone login error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Internal server error'
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
