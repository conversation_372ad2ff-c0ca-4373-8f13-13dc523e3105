import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Parse request body
    const { phone, otp, newPassword, action } = await req.json()

    if (!phone || !action) {
      return new Response(
        JSON.stringify({ error: 'Phone number and action are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Clean and format phone number
    const cleanPhone = phone.replace(/[\s()-]/g, '')
    const formattedPhone = cleanPhone.startsWith('+91') ? cleanPhone : 
                          cleanPhone.startsWith('91') ? '+' + cleanPhone : 
                          '+91' + cleanPhone

    if (action === 'send-otp') {
      // Send OTP for password reset
      console.log('Sending SMS password reset OTP to:', formattedPhone)

      // Check if user exists and is verified
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, phone_verified, full_name')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)
        .single()

      if (profileError || !profile) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Phone number not found or not verified. Please register first.'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Use SMS OTP system for password reset
      const { data, error } = await supabaseAdmin.functions.invoke('send-sms-otp', {
        body: {
          phone: formattedPhone,
          fullName: profile.full_name,
          purpose: 'password_reset'
        }
      })

      if (error || !data.success) {
        console.error('Failed to send SMS OTP:', error || data.error)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to send SMS OTP'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'SMS OTP sent successfully'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else if (action === 'verify-otp') {
      // Verify OTP for password reset
      if (!otp) {
        return new Response(
          JSON.stringify({ error: 'OTP is required for verification' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      console.log('Verifying SMS password reset OTP for:', formattedPhone)

      // Validate OTP directly from pending_whatsapp_users table
      const { data: pendingUser, error: queryError } = await supabaseAdmin
        .from('pending_whatsapp_users')
        .select('*')
        .eq('phone', formattedPhone)
        .eq('purpose', 'password_reset')
        .single()

      if (queryError) {
        console.error('Error querying pending password reset OTP:', queryError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP validation failed'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!pendingUser) {
        console.log('No pending password reset OTP found for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'No OTP request found. Please request a new OTP.'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Check if OTP has expired
      const now = new Date()
      const expiresAt = new Date(pendingUser.otp_expires_at)

      if (now > expiresAt) {
        console.log('Expired password reset OTP for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP has expired. Please request a new OTP.'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Check if OTP matches
      if (pendingUser.otp_code !== otp) {
        console.log('Invalid password reset OTP for phone:', formattedPhone, 'Expected:', pendingUser.otp_code.substring(0, 2) + '****', 'Received:', otp.substring(0, 2) + '****')

        // Increment attempts
        const { error: updateError } = await supabaseAdmin
          .from('pending_whatsapp_users')
          .update({
            attempts: (pendingUser.attempts || 0) + 1,
            updated_at: new Date().toISOString()
          })
          .eq('phone', formattedPhone)
          .eq('purpose', 'password_reset')

        if (updateError) {
          console.error('Error updating password reset OTP attempts:', updateError)
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Invalid OTP. Please check and try again.'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Check if too many attempts
      if ((pendingUser.attempts || 0) >= 3) {
        console.log('Too many password reset OTP attempts for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Too many failed attempts. Please request a new OTP.'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'OTP verified successfully. You can now reset your password.',
          phone: formattedPhone
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else if (action === 'reset-password') {
      // Reset password after OTP verification
      if (!otp || !newPassword) {
        return new Response(
          JSON.stringify({ error: 'OTP and new password are required' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      console.log('Resetting password for:', formattedPhone)

      // Verify OTP again for security - direct database validation
      const { data: pendingUser, error: queryError } = await supabaseAdmin
        .from('pending_whatsapp_users')
        .select('*')
        .eq('phone', formattedPhone)
        .eq('purpose', 'password_reset')
        .single()

      if (queryError || !pendingUser) {
        console.error('Error querying pending password reset OTP for reset:', queryError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'No valid OTP request found. Please request a new OTP.'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Check if OTP has expired
      const now = new Date()
      const expiresAt = new Date(pendingUser.otp_expires_at)

      if (now > expiresAt) {
        console.log('Expired password reset OTP during reset for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP has expired. Please request a new OTP.'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Check if OTP matches
      if (pendingUser.otp_code !== otp) {
        console.log('Invalid password reset OTP during reset for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Invalid or expired OTP'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Check if too many attempts
      if ((pendingUser.attempts || 0) >= 3) {
        console.log('Too many password reset OTP attempts during reset for phone:', formattedPhone)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Too many failed attempts. Please request a new OTP.'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Get user profile
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)
        .single()

      if (profileError || !profile) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User profile not found'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Update password using Supabase Auth Admin API
      const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
        profile.id,
        { password: newPassword }
      )

      if (updateError) {
        console.error('Password update error:', updateError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to update password'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('Password updated successfully for user:', profile.id)

      // Clean up pending SMS OTP record
      const { error: cleanupError } = await supabaseAdmin
        .from('pending_whatsapp_users')
        .delete()
        .eq('phone', formattedPhone)
        .eq('purpose', 'password_reset')

      if (cleanupError) {
        console.error('Error cleaning up pending SMS OTP:', cleanupError)
        // Don't fail the request, cleanup is not critical
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Password reset successfully'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else {
      return new Response(
        JSON.stringify({ error: 'Invalid action. Must be "send-otp", "verify-otp", or "reset-password"' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }
  } catch (error) {
    console.error('Error in forgot-password-sms function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
